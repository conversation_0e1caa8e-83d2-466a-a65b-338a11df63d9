cmake_minimum_required(VERSION 3.31)
project(bricklayer C)

set(CMAKE_C_STANDARD 23)

# Source files
set(SOURCES
    main.c
    game.c
    player.c
    input.c
    renderer.c
    physics.c
)

# Header files
set(HEADERS
    game.h
    player.h
    input.h
    renderer.h
    physics.h
)

add_executable(bricklayer ${SOURCES} ${HEADERS})

# Test executable
set(TEST_SOURCES
    test_bricklayer.c
    game.c
    player.c
    physics.c
    # Note: excluding input.c and renderer.c as they have terminal dependencies
)

add_executable(test_bricklayer ${TEST_SOURCES} ${HEADERS})
