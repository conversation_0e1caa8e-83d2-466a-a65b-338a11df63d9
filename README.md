# Bricklayer Game

A text-based bricklayer game written in C where the player controls a character who lays bricks to build structures.

## Features

✅ **Player Movement and Controls**
- Move with WASD keys
- Responsive character movement with collision detection

✅ **Brick Placement System**
- Place bricks with SPACE key
- Collision detection prevents placing bricks on occupied spaces
- Player carries a limited number of bricks

✅ **Basic Game Physics**
- Gravity system for falling bricks
- Unsupported bricks automatically fall
- Realistic brick settling mechanics

✅ **Scoring System**
- Points for placing bricks
- Bonus points for completing lines
- Level progression based on lines completed
- Score multipliers for multiple line clears

✅ **Game State Management**
- Menu system (start, quit)
- Playing state with pause functionality
- Game over state with restart option
- Clean state transitions

✅ **Text-Based Graphics**
- ASCII art game world
- Clear visual representation of player (@), bricks (#), and falling bricks (*)
- Informative HUD showing score, level, and brick count
- Attractive menu and game over screens

## Game Controls

| Key | Action |
|-----|--------|
| **WASD** | Move player (W=Up, A=Left, S=Down, D=Right) |
| **SPACE** | Place brick below player |
| **ESC** | Pause/unpause game |
| **R** | Restart game |
| **Q** | Quit game |
| **ENTER** | Start game from menu |

## Game Mechanics

### Objective
Build stable structures by placing bricks. Complete horizontal lines to score points and advance levels.

### Gameplay
1. **Movement**: Navigate your character (@) around the game world
2. **Brick Placement**: Use SPACE to place bricks below your current position
3. **Physics**: Unsupported bricks will fall due to gravity
4. **Line Clearing**: Complete full horizontal lines to score points and gain more bricks
5. **Progression**: Advance levels by clearing lines, with increasing score multipliers

### Scoring
- **Brick Placement**: 10 points per brick placed
- **Line Completion**: 100 × lines cleared × line multiplier × level
  - 1 line: 1× multiplier
  - 2 lines: 3× multiplier  
  - 3 lines: 5× multiplier
  - 4+ lines: 2× per line multiplier

## Building and Running

### Prerequisites
- CMake 3.31 or higher
- C compiler with C23 support (GCC 13+ recommended)
- Linux/Unix environment (for terminal input handling)

### Build Instructions

1. **Clone/Navigate to the project directory**
   ```bash
   cd /path/to/bricklayer
   ```

2. **Create build directory and compile**
   ```bash
   mkdir -p build
   cd build
   cmake ..
   make
   ```

3. **Run the game**
   ```bash
   ./bricklayer
   ```

### Running Tests

To verify the game functionality:

```bash
cd build
make test_bricklayer
./test_bricklayer
```

The test suite includes:
- Game initialization tests
- Player movement validation
- Brick placement mechanics
- Collision detection
- Physics system verification
- Scoring system validation

## Project Structure

```
bricklayer/
├── main.c              # Main game loop and initialization
├── game.h/game.c       # Core game logic and state management
├── player.h/player.c   # Player movement and controls
├── input.h/input.c     # Input handling system
├── renderer.h/renderer.c # Display and graphics rendering
├── physics.h/physics.c # Collision detection and gravity
├── test_bricklayer.c   # Unit tests
├── CMakeLists.txt      # Build configuration
└── README.md           # This file
```

## Code Quality

The codebase follows good C programming practices:

- **Modular Design**: Separated concerns into logical modules
- **Clean Interfaces**: Well-defined header files with clear function signatures
- **Memory Safety**: Careful bounds checking and validation
- **Error Handling**: Robust input validation and edge case handling
- **Documentation**: Comprehensive comments and clear naming conventions
- **Testing**: Extensive unit test coverage for core functionality

## Future Enhancements

Potential improvements for the game:
- [ ] Graphical version using SDL or similar library
- [ ] Sound effects and background music
- [ ] Different brick types with special properties
- [ ] Power-ups and special abilities
- [ ] Multiplayer support
- [ ] Save/load game functionality
- [ ] High score tracking
- [ ] More sophisticated physics (brick rotation, etc.)

## License

This project is open source. Feel free to modify and distribute as needed.

---

**Enjoy building with bricks!** 🧱
