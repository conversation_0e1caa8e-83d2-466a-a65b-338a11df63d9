# Bricklayer Game

A text-based bricklayer game written in C where the player controls a character who lays bricks to build structures.

## Features

✅ **Enhanced Player Movement and Controls**
- Responsive WASD movement with improved input handling
- Key repeat support for smooth continuous movement
- Collision detection prevents movement into occupied spaces
- No input lag or missed keystrokes

✅ **Improved Brick Placement System**
- Place bricks with SPACE key (single-press activation)
- Advanced collision detection prevents placing bricks on occupied spaces
- Player carries a limited number of bricks with automatic replenishment
- Visual feedback for successful brick placement

✅ **Advanced Game Physics**
- Gravity system for falling bricks with proper timing
- Unsupported bricks automatically fall with realistic physics
- Brick settling mechanics with stability checking
- Edge case handling prevents physics glitches

✅ **Comprehensive Scoring System**
- Points for placing bricks (10 points each)
- Bonus points for completing lines with level multipliers
- Level progression based on lines completed (every 10 lines)
- Score multipliers for multiple simultaneous line clears

✅ **Robust Game State Management**
- Smooth transitions between menu → playing → paused → game over
- Proper state handling with single-press key activation
- Pause/unpause functionality (ESC key)
- Restart capability (R key) and clean exit (Q key)

✅ **Optimized Text-Based Graphics**
- Flicker-free rendering with smart screen updates
- Clear visual representation: player (@), bricks (#), falling bricks (*)
- Real-time HUD showing score, level, lines completed, and brick count
- Attractive menu and game over screens with proper formatting
- Hidden cursor during gameplay for cleaner appearance

## Game Controls

| Key | Action |
|-----|--------|
| **WASD** | Move player (W=Up, A=Left, S=Down, D=Right) |
| **SPACE** | Place brick below player |
| **ESC** | Pause/unpause game |
| **R** | Restart game |
| **Q** | Quit game |
| **ENTER** | Start game from menu |

## Game Mechanics

### Objective
Build stable structures by placing bricks. Complete horizontal lines to score points and advance levels.

### Gameplay
1. **Movement**: Navigate your character (@) around the game world
2. **Brick Placement**: Use SPACE to place bricks below your current position
3. **Physics**: Unsupported bricks will fall due to gravity
4. **Line Clearing**: Complete full horizontal lines to score points and gain more bricks
5. **Progression**: Advance levels by clearing lines, with increasing score multipliers

### Scoring
- **Brick Placement**: 10 points per brick placed
- **Line Completion**: 100 × lines cleared × line multiplier × level
  - 1 line: 1× multiplier
  - 2 lines: 3× multiplier
  - 3 lines: 5× multiplier
  - 4+ lines: 2× per line multiplier

### Enhanced Gameplay Tips

1. **Movement Strategy**: Hold WASD keys for continuous movement - the improved input system supports smooth key repeat
2. **Brick Management**: You start with 5 bricks and get more when completing lines. If you run out, you'll automatically get 3 more
3. **Building Technique**: Build stable foundations first - unsupported bricks will fall due to gravity
4. **Line Clearing**: Focus on completing full horizontal lines for maximum points and brick rewards
5. **Pause Feature**: Use ESC to pause anytime during gameplay - perfect for taking breaks
6. **Quick Restart**: Press R at any time during gameplay to restart with a fresh game

## Building and Running

### Prerequisites
- CMake 3.31 or higher
- C compiler with C23 support (GCC 13+ recommended)
- Linux/Unix environment (for terminal input handling)
- Terminal with ANSI escape sequence support

### Build Instructions

1. **Clone/Navigate to the project directory**
   ```bash
   cd /path/to/bricklayer
   ```

2. **Create build directory and compile**
   ```bash
   mkdir -p build
   cd build
   cmake ..
   make
   ```

3. **Run the game**
   ```bash
   ./bricklayer
   ```

### Enhanced Gameplay Experience

The improved version includes several usability enhancements:

- **Responsive Controls**: Movement keys (WASD) support key repeat for smooth movement
- **Single-Press Actions**: Action keys (SPACE, ESC, R, Q) activate on single press to prevent accidental double-actions
- **Reduced Flicker**: Optimized rendering system minimizes screen flicker
- **Better Terminal Compatibility**: Improved error handling for different terminal environments
- **Smooth State Transitions**: Clean transitions between game states

### Running Tests

To verify the game functionality:

```bash
cd build
make test_bricklayer
./test_bricklayer
```

The test suite includes:
- Game initialization tests
- Player movement validation
- Brick placement mechanics
- Collision detection
- Physics system verification
- Scoring system validation

## Troubleshooting

### Terminal Compatibility Issues

If you see warnings like "Could not get terminal attributes":
- This is usually harmless and the game will still work
- Ensure you're running in a proper terminal (not an IDE console)
- Try running in a different terminal emulator if issues persist

### Input Not Responding

If keys don't seem to work:
- Make sure the terminal window has focus
- Try pressing keys gently - the game uses single-press detection
- For movement, you can hold down WASD keys for continuous movement
- For actions (SPACE, ESC, R, Q), press once and release

### Display Issues

If the game display looks corrupted:
- Ensure your terminal supports ANSI escape sequences
- Try resizing the terminal window
- Most modern terminals (gnome-terminal, xterm, etc.) work fine

### Performance Issues

If the game runs too fast or slow:
- The game is designed to run at ~20 FPS with reduced render frequency
- Performance may vary on different systems
- Close other applications if the system is under heavy load

## Project Structure

```
bricklayer/
├── main.c              # Main game loop and initialization
├── game.h/game.c       # Core game logic and state management
├── player.h/player.c   # Player movement and controls
├── input.h/input.c     # Input handling system
├── renderer.h/renderer.c # Display and graphics rendering
├── physics.h/physics.c # Collision detection and gravity
├── test_bricklayer.c   # Unit tests
├── CMakeLists.txt      # Build configuration
└── README.md           # This file
```

## Code Quality

The codebase follows good C programming practices:

- **Modular Design**: Separated concerns into logical modules
- **Clean Interfaces**: Well-defined header files with clear function signatures
- **Memory Safety**: Careful bounds checking and validation
- **Error Handling**: Robust input validation and edge case handling
- **Documentation**: Comprehensive comments and clear naming conventions
- **Testing**: Extensive unit test coverage for core functionality

## Future Enhancements

Potential improvements for the game:
- [ ] Graphical version using SDL or similar library
- [ ] Sound effects and background music
- [ ] Different brick types with special properties
- [ ] Power-ups and special abilities
- [ ] Multiplayer support
- [ ] Save/load game functionality
- [ ] High score tracking
- [ ] More sophisticated physics (brick rotation, etc.)

## License

This project is open source. Feel free to modify and distribute as needed.

---

**Enjoy building with bricks!** 🧱
