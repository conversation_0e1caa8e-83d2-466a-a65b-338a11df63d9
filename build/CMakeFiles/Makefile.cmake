# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "Unix Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "/home/<USER>/CLionProjects/bricklayer/CMakeLists.txt"
  "CMakeFiles/4.0.3/CMakeCCompiler.cmake"
  "CMakeFiles/4.0.3/CMakeSystem.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/CMakeCInformation.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/CMakeGenericSystem.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/CMakeInitializeConfigs.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/CMakeSystemSpecificInformation.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/CMakeSystemSpecificInitialize.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/Compiler/GNU-C.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/Compiler/GNU.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/Internal/CMakeCLinkerInformation.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/Linker/GNU-C.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/Linker/GNU.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/Platform/Linker/GNU.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/Platform/Linker/Linux-GNU-C.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/Platform/Linker/Linux-GNU.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/Platform/Linux-GNU-C.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/Platform/Linux-GNU.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/Platform/Linux-Initialize.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/Platform/Linux.cmake"
  "/snap/cmake/1468/share/cmake-4.0/Modules/Platform/UnixPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/bricklayer.dir/DependInfo.cmake"
  "CMakeFiles/test_bricklayer.dir/DependInfo.cmake"
  )
