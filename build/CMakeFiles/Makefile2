# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /snap/cmake/1468/bin/cmake

# The command to remove a file.
RM = /snap/cmake/1468/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/CLionProjects/bricklayer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/CLionProjects/bricklayer/build

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/bricklayer.dir/all
all: CMakeFiles/test_bricklayer.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/bricklayer.dir/codegen
codegen: CMakeFiles/test_bricklayer.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/bricklayer.dir/clean
clean: CMakeFiles/test_bricklayer.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/bricklayer.dir

# All Build rule for target.
CMakeFiles/bricklayer.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/CLionProjects/bricklayer/build/CMakeFiles --progress-num=1,2,3,4,5,6,7 "Built target bricklayer"
.PHONY : CMakeFiles/bricklayer.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/bricklayer.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/bricklayer/build/CMakeFiles 7
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/bricklayer.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/bricklayer/build/CMakeFiles 0
.PHONY : CMakeFiles/bricklayer.dir/rule

# Convenience name for target.
bricklayer: CMakeFiles/bricklayer.dir/rule
.PHONY : bricklayer

# codegen rule for target.
CMakeFiles/bricklayer.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/CLionProjects/bricklayer/build/CMakeFiles --progress-num=1,2,3,4,5,6,7 "Finished codegen for target bricklayer"
.PHONY : CMakeFiles/bricklayer.dir/codegen

# clean rule for target.
CMakeFiles/bricklayer.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/clean
.PHONY : CMakeFiles/bricklayer.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/test_bricklayer.dir

# All Build rule for target.
CMakeFiles/test_bricklayer.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_bricklayer.dir/build.make CMakeFiles/test_bricklayer.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_bricklayer.dir/build.make CMakeFiles/test_bricklayer.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/CLionProjects/bricklayer/build/CMakeFiles --progress-num=8,9,10,11,12 "Built target test_bricklayer"
.PHONY : CMakeFiles/test_bricklayer.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/test_bricklayer.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/bricklayer/build/CMakeFiles 5
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/test_bricklayer.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/bricklayer/build/CMakeFiles 0
.PHONY : CMakeFiles/test_bricklayer.dir/rule

# Convenience name for target.
test_bricklayer: CMakeFiles/test_bricklayer.dir/rule
.PHONY : test_bricklayer

# codegen rule for target.
CMakeFiles/test_bricklayer.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_bricklayer.dir/build.make CMakeFiles/test_bricklayer.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/CLionProjects/bricklayer/build/CMakeFiles --progress-num=8,9,10,11,12 "Finished codegen for target test_bricklayer"
.PHONY : CMakeFiles/test_bricklayer.dir/codegen

# clean rule for target.
CMakeFiles/test_bricklayer.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_bricklayer.dir/build.make CMakeFiles/test_bricklayer.dir/clean
.PHONY : CMakeFiles/test_bricklayer.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

