# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /snap/cmake/1468/bin/cmake

# The command to remove a file.
RM = /snap/cmake/1468/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/CLionProjects/bricklayer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/CLionProjects/bricklayer/build

# Include any dependencies generated for this target.
include CMakeFiles/bricklayer.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/bricklayer.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/bricklayer.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/bricklayer.dir/flags.make

CMakeFiles/bricklayer.dir/codegen:
.PHONY : CMakeFiles/bricklayer.dir/codegen

CMakeFiles/bricklayer.dir/main.c.o: CMakeFiles/bricklayer.dir/flags.make
CMakeFiles/bricklayer.dir/main.c.o: /home/<USER>/CLionProjects/bricklayer/main.c
CMakeFiles/bricklayer.dir/main.c.o: CMakeFiles/bricklayer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/CLionProjects/bricklayer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/bricklayer.dir/main.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/bricklayer.dir/main.c.o -MF CMakeFiles/bricklayer.dir/main.c.o.d -o CMakeFiles/bricklayer.dir/main.c.o -c /home/<USER>/CLionProjects/bricklayer/main.c

CMakeFiles/bricklayer.dir/main.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/bricklayer.dir/main.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/CLionProjects/bricklayer/main.c > CMakeFiles/bricklayer.dir/main.c.i

CMakeFiles/bricklayer.dir/main.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/bricklayer.dir/main.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/CLionProjects/bricklayer/main.c -o CMakeFiles/bricklayer.dir/main.c.s

CMakeFiles/bricklayer.dir/game.c.o: CMakeFiles/bricklayer.dir/flags.make
CMakeFiles/bricklayer.dir/game.c.o: /home/<USER>/CLionProjects/bricklayer/game.c
CMakeFiles/bricklayer.dir/game.c.o: CMakeFiles/bricklayer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/CLionProjects/bricklayer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/bricklayer.dir/game.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/bricklayer.dir/game.c.o -MF CMakeFiles/bricklayer.dir/game.c.o.d -o CMakeFiles/bricklayer.dir/game.c.o -c /home/<USER>/CLionProjects/bricklayer/game.c

CMakeFiles/bricklayer.dir/game.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/bricklayer.dir/game.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/CLionProjects/bricklayer/game.c > CMakeFiles/bricklayer.dir/game.c.i

CMakeFiles/bricklayer.dir/game.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/bricklayer.dir/game.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/CLionProjects/bricklayer/game.c -o CMakeFiles/bricklayer.dir/game.c.s

CMakeFiles/bricklayer.dir/player.c.o: CMakeFiles/bricklayer.dir/flags.make
CMakeFiles/bricklayer.dir/player.c.o: /home/<USER>/CLionProjects/bricklayer/player.c
CMakeFiles/bricklayer.dir/player.c.o: CMakeFiles/bricklayer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/CLionProjects/bricklayer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/bricklayer.dir/player.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/bricklayer.dir/player.c.o -MF CMakeFiles/bricklayer.dir/player.c.o.d -o CMakeFiles/bricklayer.dir/player.c.o -c /home/<USER>/CLionProjects/bricklayer/player.c

CMakeFiles/bricklayer.dir/player.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/bricklayer.dir/player.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/CLionProjects/bricklayer/player.c > CMakeFiles/bricklayer.dir/player.c.i

CMakeFiles/bricklayer.dir/player.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/bricklayer.dir/player.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/CLionProjects/bricklayer/player.c -o CMakeFiles/bricklayer.dir/player.c.s

CMakeFiles/bricklayer.dir/input.c.o: CMakeFiles/bricklayer.dir/flags.make
CMakeFiles/bricklayer.dir/input.c.o: /home/<USER>/CLionProjects/bricklayer/input.c
CMakeFiles/bricklayer.dir/input.c.o: CMakeFiles/bricklayer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/CLionProjects/bricklayer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/bricklayer.dir/input.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/bricklayer.dir/input.c.o -MF CMakeFiles/bricklayer.dir/input.c.o.d -o CMakeFiles/bricklayer.dir/input.c.o -c /home/<USER>/CLionProjects/bricklayer/input.c

CMakeFiles/bricklayer.dir/input.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/bricklayer.dir/input.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/CLionProjects/bricklayer/input.c > CMakeFiles/bricklayer.dir/input.c.i

CMakeFiles/bricklayer.dir/input.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/bricklayer.dir/input.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/CLionProjects/bricklayer/input.c -o CMakeFiles/bricklayer.dir/input.c.s

CMakeFiles/bricklayer.dir/renderer.c.o: CMakeFiles/bricklayer.dir/flags.make
CMakeFiles/bricklayer.dir/renderer.c.o: /home/<USER>/CLionProjects/bricklayer/renderer.c
CMakeFiles/bricklayer.dir/renderer.c.o: CMakeFiles/bricklayer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/CLionProjects/bricklayer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building C object CMakeFiles/bricklayer.dir/renderer.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/bricklayer.dir/renderer.c.o -MF CMakeFiles/bricklayer.dir/renderer.c.o.d -o CMakeFiles/bricklayer.dir/renderer.c.o -c /home/<USER>/CLionProjects/bricklayer/renderer.c

CMakeFiles/bricklayer.dir/renderer.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/bricklayer.dir/renderer.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/CLionProjects/bricklayer/renderer.c > CMakeFiles/bricklayer.dir/renderer.c.i

CMakeFiles/bricklayer.dir/renderer.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/bricklayer.dir/renderer.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/CLionProjects/bricklayer/renderer.c -o CMakeFiles/bricklayer.dir/renderer.c.s

CMakeFiles/bricklayer.dir/physics.c.o: CMakeFiles/bricklayer.dir/flags.make
CMakeFiles/bricklayer.dir/physics.c.o: /home/<USER>/CLionProjects/bricklayer/physics.c
CMakeFiles/bricklayer.dir/physics.c.o: CMakeFiles/bricklayer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/CLionProjects/bricklayer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building C object CMakeFiles/bricklayer.dir/physics.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/bricklayer.dir/physics.c.o -MF CMakeFiles/bricklayer.dir/physics.c.o.d -o CMakeFiles/bricklayer.dir/physics.c.o -c /home/<USER>/CLionProjects/bricklayer/physics.c

CMakeFiles/bricklayer.dir/physics.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/bricklayer.dir/physics.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/CLionProjects/bricklayer/physics.c > CMakeFiles/bricklayer.dir/physics.c.i

CMakeFiles/bricklayer.dir/physics.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/bricklayer.dir/physics.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/CLionProjects/bricklayer/physics.c -o CMakeFiles/bricklayer.dir/physics.c.s

# Object files for target bricklayer
bricklayer_OBJECTS = \
"CMakeFiles/bricklayer.dir/main.c.o" \
"CMakeFiles/bricklayer.dir/game.c.o" \
"CMakeFiles/bricklayer.dir/player.c.o" \
"CMakeFiles/bricklayer.dir/input.c.o" \
"CMakeFiles/bricklayer.dir/renderer.c.o" \
"CMakeFiles/bricklayer.dir/physics.c.o"

# External object files for target bricklayer
bricklayer_EXTERNAL_OBJECTS =

bricklayer: CMakeFiles/bricklayer.dir/main.c.o
bricklayer: CMakeFiles/bricklayer.dir/game.c.o
bricklayer: CMakeFiles/bricklayer.dir/player.c.o
bricklayer: CMakeFiles/bricklayer.dir/input.c.o
bricklayer: CMakeFiles/bricklayer.dir/renderer.c.o
bricklayer: CMakeFiles/bricklayer.dir/physics.c.o
bricklayer: CMakeFiles/bricklayer.dir/build.make
bricklayer: CMakeFiles/bricklayer.dir/compiler_depend.ts
bricklayer: CMakeFiles/bricklayer.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/CLionProjects/bricklayer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Linking C executable bricklayer"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/bricklayer.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/bricklayer.dir/build: bricklayer
.PHONY : CMakeFiles/bricklayer.dir/build

CMakeFiles/bricklayer.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/bricklayer.dir/cmake_clean.cmake
.PHONY : CMakeFiles/bricklayer.dir/clean

CMakeFiles/bricklayer.dir/depend:
	cd /home/<USER>/CLionProjects/bricklayer/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/CLionProjects/bricklayer /home/<USER>/CLionProjects/bricklayer /home/<USER>/CLionProjects/bricklayer/build /home/<USER>/CLionProjects/bricklayer/build /home/<USER>/CLionProjects/bricklayer/build/CMakeFiles/bricklayer.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/bricklayer.dir/depend

