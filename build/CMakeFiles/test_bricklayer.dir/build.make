# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /snap/cmake/1468/bin/cmake

# The command to remove a file.
RM = /snap/cmake/1468/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/CLionProjects/bricklayer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/CLionProjects/bricklayer/build

# Include any dependencies generated for this target.
include CMakeFiles/test_bricklayer.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/test_bricklayer.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test_bricklayer.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_bricklayer.dir/flags.make

CMakeFiles/test_bricklayer.dir/codegen:
.PHONY : CMakeFiles/test_bricklayer.dir/codegen

CMakeFiles/test_bricklayer.dir/test_bricklayer.c.o: CMakeFiles/test_bricklayer.dir/flags.make
CMakeFiles/test_bricklayer.dir/test_bricklayer.c.o: /home/<USER>/CLionProjects/bricklayer/test_bricklayer.c
CMakeFiles/test_bricklayer.dir/test_bricklayer.c.o: CMakeFiles/test_bricklayer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/CLionProjects/bricklayer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/test_bricklayer.dir/test_bricklayer.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/test_bricklayer.dir/test_bricklayer.c.o -MF CMakeFiles/test_bricklayer.dir/test_bricklayer.c.o.d -o CMakeFiles/test_bricklayer.dir/test_bricklayer.c.o -c /home/<USER>/CLionProjects/bricklayer/test_bricklayer.c

CMakeFiles/test_bricklayer.dir/test_bricklayer.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/test_bricklayer.dir/test_bricklayer.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/CLionProjects/bricklayer/test_bricklayer.c > CMakeFiles/test_bricklayer.dir/test_bricklayer.c.i

CMakeFiles/test_bricklayer.dir/test_bricklayer.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/test_bricklayer.dir/test_bricklayer.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/CLionProjects/bricklayer/test_bricklayer.c -o CMakeFiles/test_bricklayer.dir/test_bricklayer.c.s

CMakeFiles/test_bricklayer.dir/game.c.o: CMakeFiles/test_bricklayer.dir/flags.make
CMakeFiles/test_bricklayer.dir/game.c.o: /home/<USER>/CLionProjects/bricklayer/game.c
CMakeFiles/test_bricklayer.dir/game.c.o: CMakeFiles/test_bricklayer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/CLionProjects/bricklayer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/test_bricklayer.dir/game.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/test_bricklayer.dir/game.c.o -MF CMakeFiles/test_bricklayer.dir/game.c.o.d -o CMakeFiles/test_bricklayer.dir/game.c.o -c /home/<USER>/CLionProjects/bricklayer/game.c

CMakeFiles/test_bricklayer.dir/game.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/test_bricklayer.dir/game.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/CLionProjects/bricklayer/game.c > CMakeFiles/test_bricklayer.dir/game.c.i

CMakeFiles/test_bricklayer.dir/game.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/test_bricklayer.dir/game.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/CLionProjects/bricklayer/game.c -o CMakeFiles/test_bricklayer.dir/game.c.s

CMakeFiles/test_bricklayer.dir/player.c.o: CMakeFiles/test_bricklayer.dir/flags.make
CMakeFiles/test_bricklayer.dir/player.c.o: /home/<USER>/CLionProjects/bricklayer/player.c
CMakeFiles/test_bricklayer.dir/player.c.o: CMakeFiles/test_bricklayer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/CLionProjects/bricklayer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/test_bricklayer.dir/player.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/test_bricklayer.dir/player.c.o -MF CMakeFiles/test_bricklayer.dir/player.c.o.d -o CMakeFiles/test_bricklayer.dir/player.c.o -c /home/<USER>/CLionProjects/bricklayer/player.c

CMakeFiles/test_bricklayer.dir/player.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/test_bricklayer.dir/player.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/CLionProjects/bricklayer/player.c > CMakeFiles/test_bricklayer.dir/player.c.i

CMakeFiles/test_bricklayer.dir/player.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/test_bricklayer.dir/player.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/CLionProjects/bricklayer/player.c -o CMakeFiles/test_bricklayer.dir/player.c.s

CMakeFiles/test_bricklayer.dir/physics.c.o: CMakeFiles/test_bricklayer.dir/flags.make
CMakeFiles/test_bricklayer.dir/physics.c.o: /home/<USER>/CLionProjects/bricklayer/physics.c
CMakeFiles/test_bricklayer.dir/physics.c.o: CMakeFiles/test_bricklayer.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/CLionProjects/bricklayer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building C object CMakeFiles/test_bricklayer.dir/physics.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -MD -MT CMakeFiles/test_bricklayer.dir/physics.c.o -MF CMakeFiles/test_bricklayer.dir/physics.c.o.d -o CMakeFiles/test_bricklayer.dir/physics.c.o -c /home/<USER>/CLionProjects/bricklayer/physics.c

CMakeFiles/test_bricklayer.dir/physics.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing C source to CMakeFiles/test_bricklayer.dir/physics.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /home/<USER>/CLionProjects/bricklayer/physics.c > CMakeFiles/test_bricklayer.dir/physics.c.i

CMakeFiles/test_bricklayer.dir/physics.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling C source to assembly CMakeFiles/test_bricklayer.dir/physics.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /home/<USER>/CLionProjects/bricklayer/physics.c -o CMakeFiles/test_bricklayer.dir/physics.c.s

# Object files for target test_bricklayer
test_bricklayer_OBJECTS = \
"CMakeFiles/test_bricklayer.dir/test_bricklayer.c.o" \
"CMakeFiles/test_bricklayer.dir/game.c.o" \
"CMakeFiles/test_bricklayer.dir/player.c.o" \
"CMakeFiles/test_bricklayer.dir/physics.c.o"

# External object files for target test_bricklayer
test_bricklayer_EXTERNAL_OBJECTS =

test_bricklayer: CMakeFiles/test_bricklayer.dir/test_bricklayer.c.o
test_bricklayer: CMakeFiles/test_bricklayer.dir/game.c.o
test_bricklayer: CMakeFiles/test_bricklayer.dir/player.c.o
test_bricklayer: CMakeFiles/test_bricklayer.dir/physics.c.o
test_bricklayer: CMakeFiles/test_bricklayer.dir/build.make
test_bricklayer: CMakeFiles/test_bricklayer.dir/compiler_depend.ts
test_bricklayer: CMakeFiles/test_bricklayer.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/CLionProjects/bricklayer/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Linking C executable test_bricklayer"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/test_bricklayer.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/test_bricklayer.dir/build: test_bricklayer
.PHONY : CMakeFiles/test_bricklayer.dir/build

CMakeFiles/test_bricklayer.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/test_bricklayer.dir/cmake_clean.cmake
.PHONY : CMakeFiles/test_bricklayer.dir/clean

CMakeFiles/test_bricklayer.dir/depend:
	cd /home/<USER>/CLionProjects/bricklayer/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/CLionProjects/bricklayer /home/<USER>/CLionProjects/bricklayer /home/<USER>/CLionProjects/bricklayer/build /home/<USER>/CLionProjects/bricklayer/build /home/<USER>/CLionProjects/bricklayer/build/CMakeFiles/test_bricklayer.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/test_bricklayer.dir/depend

