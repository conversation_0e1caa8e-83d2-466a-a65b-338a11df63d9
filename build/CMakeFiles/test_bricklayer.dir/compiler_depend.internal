# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

CMakeFiles/test_bricklayer.dir/game.c.o
 /home/<USER>/CLionProjects/bricklayer/game.c
 /home/<USER>/CLionProjects/bricklayer/game.h
 /home/<USER>/CLionProjects/bricklayer/physics.h
 /home/<USER>/CLionProjects/bricklayer/player.h
 /usr/include/features-time64.h
 /usr/include/features.h
 /usr/include/stdc-predef.h
 /usr/include/stdio.h
 /usr/include/string.h
 /usr/include/strings.h
 /usr/include/time.h
 /usr/include/x86_64-linux-gnu/bits/endian.h
 /usr/include/x86_64-linux-gnu/bits/endianness.h
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h
 /usr/include/x86_64-linux-gnu/bits/floatn.h
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 /usr/include/x86_64-linux-gnu/bits/long-double.h
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 /usr/include/x86_64-linux-gnu/bits/time.h
 /usr/include/x86_64-linux-gnu/bits/time64.h
 /usr/include/x86_64-linux-gnu/bits/timesize.h
 /usr/include/x86_64-linux-gnu/bits/types.h
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 /usr/include/x86_64-linux-gnu/bits/types/clock_t.h
 /usr/include/x86_64-linux-gnu/bits/types/clockid_t.h
 /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 /usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_itimerspec.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_timespec.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
 /usr/include/x86_64-linux-gnu/bits/types/time_t.h
 /usr/include/x86_64-linux-gnu/bits/types/timer_t.h
 /usr/include/x86_64-linux-gnu/bits/typesizes.h
 /usr/include/x86_64-linux-gnu/bits/wordsize.h
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 /usr/include/x86_64-linux-gnu/gnu/stubs.h
 /usr/include/x86_64-linux-gnu/sys/cdefs.h
 /usr/lib/gcc/x86_64-linux-gnu/13/include/stdarg.h
 /usr/lib/gcc/x86_64-linux-gnu/13/include/stdbool.h
 /usr/lib/gcc/x86_64-linux-gnu/13/include/stddef.h

CMakeFiles/test_bricklayer.dir/physics.c.o
 /home/<USER>/CLionProjects/bricklayer/physics.c
 /home/<USER>/CLionProjects/bricklayer/game.h
 /home/<USER>/CLionProjects/bricklayer/physics.h
 /usr/include/features-time64.h
 /usr/include/features.h
 /usr/include/stdc-predef.h
 /usr/include/stdio.h
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h
 /usr/include/x86_64-linux-gnu/bits/floatn.h
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 /usr/include/x86_64-linux-gnu/bits/long-double.h
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 /usr/include/x86_64-linux-gnu/bits/time64.h
 /usr/include/x86_64-linux-gnu/bits/timesize.h
 /usr/include/x86_64-linux-gnu/bits/types.h
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 /usr/include/x86_64-linux-gnu/bits/typesizes.h
 /usr/include/x86_64-linux-gnu/bits/wordsize.h
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 /usr/include/x86_64-linux-gnu/gnu/stubs.h
 /usr/include/x86_64-linux-gnu/sys/cdefs.h
 /usr/lib/gcc/x86_64-linux-gnu/13/include/stdarg.h
 /usr/lib/gcc/x86_64-linux-gnu/13/include/stdbool.h
 /usr/lib/gcc/x86_64-linux-gnu/13/include/stddef.h

CMakeFiles/test_bricklayer.dir/player.c.o
 /home/<USER>/CLionProjects/bricklayer/player.c
 /home/<USER>/CLionProjects/bricklayer/game.h
 /home/<USER>/CLionProjects/bricklayer/player.h
 /usr/include/features-time64.h
 /usr/include/features.h
 /usr/include/stdc-predef.h
 /usr/include/stdio.h
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h
 /usr/include/x86_64-linux-gnu/bits/floatn.h
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 /usr/include/x86_64-linux-gnu/bits/long-double.h
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 /usr/include/x86_64-linux-gnu/bits/time64.h
 /usr/include/x86_64-linux-gnu/bits/timesize.h
 /usr/include/x86_64-linux-gnu/bits/types.h
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 /usr/include/x86_64-linux-gnu/bits/typesizes.h
 /usr/include/x86_64-linux-gnu/bits/wordsize.h
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 /usr/include/x86_64-linux-gnu/gnu/stubs.h
 /usr/include/x86_64-linux-gnu/sys/cdefs.h
 /usr/lib/gcc/x86_64-linux-gnu/13/include/stdarg.h
 /usr/lib/gcc/x86_64-linux-gnu/13/include/stdbool.h
 /usr/lib/gcc/x86_64-linux-gnu/13/include/stddef.h

CMakeFiles/test_bricklayer.dir/test_bricklayer.c.o
 /home/<USER>/CLionProjects/bricklayer/test_bricklayer.c
 /home/<USER>/CLionProjects/bricklayer/game.h
 /home/<USER>/CLionProjects/bricklayer/physics.h
 /home/<USER>/CLionProjects/bricklayer/player.h
 /usr/include/assert.h
 /usr/include/features-time64.h
 /usr/include/features.h
 /usr/include/stdc-predef.h
 /usr/include/stdio.h
 /usr/include/string.h
 /usr/include/strings.h
 /usr/include/x86_64-linux-gnu/bits/floatn-common.h
 /usr/include/x86_64-linux-gnu/bits/floatn.h
 /usr/include/x86_64-linux-gnu/bits/libc-header-start.h
 /usr/include/x86_64-linux-gnu/bits/long-double.h
 /usr/include/x86_64-linux-gnu/bits/stdio_lim.h
 /usr/include/x86_64-linux-gnu/bits/time64.h
 /usr/include/x86_64-linux-gnu/bits/timesize.h
 /usr/include/x86_64-linux-gnu/bits/types.h
 /usr/include/x86_64-linux-gnu/bits/types/FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/__FILE.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos64_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__fpos_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__locale_t.h
 /usr/include/x86_64-linux-gnu/bits/types/__mbstate_t.h
 /usr/include/x86_64-linux-gnu/bits/types/cookie_io_functions_t.h
 /usr/include/x86_64-linux-gnu/bits/types/locale_t.h
 /usr/include/x86_64-linux-gnu/bits/types/struct_FILE.h
 /usr/include/x86_64-linux-gnu/bits/typesizes.h
 /usr/include/x86_64-linux-gnu/bits/wordsize.h
 /usr/include/x86_64-linux-gnu/gnu/stubs-64.h
 /usr/include/x86_64-linux-gnu/gnu/stubs.h
 /usr/include/x86_64-linux-gnu/sys/cdefs.h
 /usr/lib/gcc/x86_64-linux-gnu/13/include/stdarg.h
 /usr/lib/gcc/x86_64-linux-gnu/13/include/stdbool.h
 /usr/lib/gcc/x86_64-linux-gnu/13/include/stddef.h

test_bricklayer
 /lib/x86_64-linux-gnu/libc.so.6
 /lib64/ld-linux-x86-64.so.2
 /usr/lib/x86_64-linux-gnu/Scrt1.o
 /usr/lib/x86_64-linux-gnu/crti.o
 /usr/lib/x86_64-linux-gnu/crtn.o
 /usr/lib/x86_64-linux-gnu/libc.so
 /usr/lib/x86_64-linux-gnu/libgcc_s.so.1
 /usr/lib/gcc/x86_64-linux-gnu/13/crtbeginS.o
 /usr/lib/gcc/x86_64-linux-gnu/13/crtendS.o
 /usr/lib/gcc/x86_64-linux-gnu/13/libgcc.a
 /usr/lib/gcc/x86_64-linux-gnu/13/libgcc_s.so
 /usr/lib/x86_64-linux-gnu/libc_nonshared.a
 /home/<USER>/CLionProjects/bricklayer/build/CMakeFiles/test_bricklayer.dir/game.c.o
 /home/<USER>/CLionProjects/bricklayer/build/CMakeFiles/test_bricklayer.dir/physics.c.o
 /home/<USER>/CLionProjects/bricklayer/build/CMakeFiles/test_bricklayer.dir/player.c.o
 /home/<USER>/CLionProjects/bricklayer/build/CMakeFiles/test_bricklayer.dir/test_bricklayer.c.o

