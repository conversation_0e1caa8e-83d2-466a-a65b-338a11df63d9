# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 4.0

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /snap/cmake/1468/bin/cmake

# The command to remove a file.
RM = /snap/cmake/1468/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/CLionProjects/bricklayer

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/CLionProjects/bricklayer/build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/snap/cmake/1468/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/snap/cmake/1468/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/bricklayer/build/CMakeFiles /home/<USER>/CLionProjects/bricklayer/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/bricklayer/build/CMakeFiles 0
.PHONY : all

# The main codegen target
codegen: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/bricklayer/build/CMakeFiles /home/<USER>/CLionProjects/bricklayer/build//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 codegen
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/CLionProjects/bricklayer/build/CMakeFiles 0
.PHONY : codegen

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named bricklayer

# Build rule for target.
bricklayer: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 bricklayer
.PHONY : bricklayer

# fast build rule for target.
bricklayer/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/build
.PHONY : bricklayer/fast

#=============================================================================
# Target rules for targets named test_bricklayer

# Build rule for target.
test_bricklayer: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 test_bricklayer
.PHONY : test_bricklayer

# fast build rule for target.
test_bricklayer/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_bricklayer.dir/build.make CMakeFiles/test_bricklayer.dir/build
.PHONY : test_bricklayer/fast

game.o: game.c.o
.PHONY : game.o

# target to build an object file
game.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/game.c.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_bricklayer.dir/build.make CMakeFiles/test_bricklayer.dir/game.c.o
.PHONY : game.c.o

game.i: game.c.i
.PHONY : game.i

# target to preprocess a source file
game.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/game.c.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_bricklayer.dir/build.make CMakeFiles/test_bricklayer.dir/game.c.i
.PHONY : game.c.i

game.s: game.c.s
.PHONY : game.s

# target to generate assembly for a file
game.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/game.c.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_bricklayer.dir/build.make CMakeFiles/test_bricklayer.dir/game.c.s
.PHONY : game.c.s

input.o: input.c.o
.PHONY : input.o

# target to build an object file
input.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/input.c.o
.PHONY : input.c.o

input.i: input.c.i
.PHONY : input.i

# target to preprocess a source file
input.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/input.c.i
.PHONY : input.c.i

input.s: input.c.s
.PHONY : input.s

# target to generate assembly for a file
input.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/input.c.s
.PHONY : input.c.s

main.o: main.c.o
.PHONY : main.o

# target to build an object file
main.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/main.c.o
.PHONY : main.c.o

main.i: main.c.i
.PHONY : main.i

# target to preprocess a source file
main.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/main.c.i
.PHONY : main.c.i

main.s: main.c.s
.PHONY : main.s

# target to generate assembly for a file
main.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/main.c.s
.PHONY : main.c.s

physics.o: physics.c.o
.PHONY : physics.o

# target to build an object file
physics.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/physics.c.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_bricklayer.dir/build.make CMakeFiles/test_bricklayer.dir/physics.c.o
.PHONY : physics.c.o

physics.i: physics.c.i
.PHONY : physics.i

# target to preprocess a source file
physics.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/physics.c.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_bricklayer.dir/build.make CMakeFiles/test_bricklayer.dir/physics.c.i
.PHONY : physics.c.i

physics.s: physics.c.s
.PHONY : physics.s

# target to generate assembly for a file
physics.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/physics.c.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_bricklayer.dir/build.make CMakeFiles/test_bricklayer.dir/physics.c.s
.PHONY : physics.c.s

player.o: player.c.o
.PHONY : player.o

# target to build an object file
player.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/player.c.o
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_bricklayer.dir/build.make CMakeFiles/test_bricklayer.dir/player.c.o
.PHONY : player.c.o

player.i: player.c.i
.PHONY : player.i

# target to preprocess a source file
player.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/player.c.i
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_bricklayer.dir/build.make CMakeFiles/test_bricklayer.dir/player.c.i
.PHONY : player.c.i

player.s: player.c.s
.PHONY : player.s

# target to generate assembly for a file
player.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/player.c.s
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_bricklayer.dir/build.make CMakeFiles/test_bricklayer.dir/player.c.s
.PHONY : player.c.s

renderer.o: renderer.c.o
.PHONY : renderer.o

# target to build an object file
renderer.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/renderer.c.o
.PHONY : renderer.c.o

renderer.i: renderer.c.i
.PHONY : renderer.i

# target to preprocess a source file
renderer.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/renderer.c.i
.PHONY : renderer.c.i

renderer.s: renderer.c.s
.PHONY : renderer.s

# target to generate assembly for a file
renderer.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/bricklayer.dir/build.make CMakeFiles/bricklayer.dir/renderer.c.s
.PHONY : renderer.c.s

test_bricklayer.o: test_bricklayer.c.o
.PHONY : test_bricklayer.o

# target to build an object file
test_bricklayer.c.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_bricklayer.dir/build.make CMakeFiles/test_bricklayer.dir/test_bricklayer.c.o
.PHONY : test_bricklayer.c.o

test_bricklayer.i: test_bricklayer.c.i
.PHONY : test_bricklayer.i

# target to preprocess a source file
test_bricklayer.c.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_bricklayer.dir/build.make CMakeFiles/test_bricklayer.dir/test_bricklayer.c.i
.PHONY : test_bricklayer.c.i

test_bricklayer.s: test_bricklayer.c.s
.PHONY : test_bricklayer.s

# target to generate assembly for a file
test_bricklayer.c.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/test_bricklayer.dir/build.make CMakeFiles/test_bricklayer.dir/test_bricklayer.c.s
.PHONY : test_bricklayer.c.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... codegen"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... bricklayer"
	@echo "... test_bricklayer"
	@echo "... game.o"
	@echo "... game.i"
	@echo "... game.s"
	@echo "... input.o"
	@echo "... input.i"
	@echo "... input.s"
	@echo "... main.o"
	@echo "... main.i"
	@echo "... main.s"
	@echo "... physics.o"
	@echo "... physics.i"
	@echo "... physics.s"
	@echo "... player.o"
	@echo "... player.i"
	@echo "... player.s"
	@echo "... renderer.o"
	@echo "... renderer.i"
	@echo "... renderer.s"
	@echo "... test_bricklayer.o"
	@echo "... test_bricklayer.i"
	@echo "... test_bricklayer.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

