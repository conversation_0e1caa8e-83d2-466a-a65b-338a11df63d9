#!/bin/bash

# Bricklayer Game Demo Script
# This script demonstrates the improved game features

echo "🎮 Bricklayer Game - Enhanced Version Demo"
echo "=========================================="
echo ""

# Check if game is built
if [ ! -f "build/bricklayer" ]; then
    echo "❌ Game not found. Building now..."
    mkdir -p build
    cd build
    cmake ..
    make
    cd ..
    echo "✅ Build complete!"
    echo ""
fi

# Run tests first
echo "🧪 Running tests to verify functionality..."
cd build
./test_bricklayer
echo ""

# Show improvements
echo "🚀 Key Improvements in This Version:"
echo "-----------------------------------"
echo "✅ Responsive input handling with key repeat support"
echo "✅ Smooth game state transitions"
echo "✅ Reduced screen flicker with optimized rendering"
echo "✅ Better terminal compatibility and error handling"
echo "✅ Enhanced physics with edge case handling"
echo "✅ Improved user experience with single-press actions"
echo ""

echo "🎯 How to Play:"
echo "---------------"
echo "• WASD keys: Move player (hold for continuous movement)"
echo "• SPACE: Place brick (single press)"
echo "• ESC: Pause/unpause game"
echo "• R: Restart game"
echo "• Q: Quit game"
echo "• ENTER: Start game from menu"
echo ""

echo "🎮 Starting the enhanced Bricklayer game..."
echo "Press Ctrl+C to exit the demo"
echo ""

# Start the game
./bricklayer

echo ""
echo "🎉 Thanks for trying the enhanced Bricklayer game!"
echo "All improvements have been successfully implemented:"
echo "• Input responsiveness ✅"
echo "• Game flow ✅" 
echo "• Visual feedback ✅"
echo "• Physics and mechanics ✅"
echo "• Terminal compatibility ✅"
echo "• Error handling ✅"
