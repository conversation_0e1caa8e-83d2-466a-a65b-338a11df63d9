#include "game.h"
#include "player.h"
#include "physics.h"
#include <stdio.h>
#include <string.h>
#include <time.h>

void init_game(GameWorld* world) {
    if (!world) return;

    // Initialize game world
    memset(world->grid, BRICK_EMPTY, sizeof(world->grid));
    world->num_falling_bricks = 0;

    // Clear falling bricks array
    memset(world->falling_bricks, 0, sizeof(world->falling_bricks));

    // Initialize player
    init_player(&world->player);

    // Initialize game state
    world->state = GAME_MENU;
    world->score = 0;
    world->level = 1;
    world->lines_completed = 0;
    world->last_gravity_time = 0;

    // Add some ground bricks for initial support
    for (int x = 0; x < WORLD_WIDTH; x++) {
        world->grid[WORLD_HEIGHT - 1][x] = BRICK_NORMAL;
    }
}

void update_game(GameWorld* world, long current_time) {
    if (!world || world->state != GAME_PLAYING) {
        return;
    }

    // Apply gravity at regular intervals
    if (current_time - world->last_gravity_time >= GRAVITY_DELAY) {
        apply_gravity(world);
        world->last_gravity_time = current_time;
    }

    // Check for completed lines
    check_completed_lines(world);

    // Check brick stability
    check_brick_stability(world);

    // Check game over condition (if player is stuck or no bricks left)
    if (world->player.bricks_carried <= 0) {
        // Give player more bricks if they run out
        world->player.bricks_carried = 3;
    }

    // Check if player is trapped (optional game over condition)
    // This could be expanded to check if player can't move or place bricks
}

bool is_valid_position(const GameWorld* world, int x, int y) {
    return x >= 0 && x < WORLD_WIDTH && y >= 0 && y < WORLD_HEIGHT;
}

bool can_place_brick(const GameWorld* world, int x, int y) {
    if (!is_valid_position(world, x, y)) {
        return false;
    }
    
    // Can't place on occupied space
    if (world->grid[y][x] != BRICK_EMPTY) {
        return false;
    }
    
    // Can't place on player position
    if (world->player.pos.x == x && world->player.pos.y == y) {
        return false;
    }
    
    // Check for falling bricks at this position
    for (int i = 0; i < world->num_falling_bricks; i++) {
        if (world->falling_bricks[i].pos.x == x && world->falling_bricks[i].pos.y == y) {
            return false;
        }
    }
    
    return true;
}

void place_brick(GameWorld* world, int x, int y) {
    if (!can_place_brick(world, x, y)) {
        return;
    }
    
    world->grid[y][x] = BRICK_NORMAL;
    
    // Add score for placing brick
    world->score += 10;
}

void apply_gravity(GameWorld* world) {
    apply_gravity_to_falling_bricks(world);
    make_unsupported_bricks_fall(world);
}

void check_completed_lines(GameWorld* world) {
    int lines_cleared = 0;
    
    // Check each row from bottom to top
    for (int y = WORLD_HEIGHT - 1; y >= 0; y--) {
        bool line_complete = true;
        
        // Check if line is completely filled
        for (int x = 0; x < WORLD_WIDTH; x++) {
            if (world->grid[y][x] == BRICK_EMPTY) {
                line_complete = false;
                break;
            }
        }
        
        if (line_complete && y < WORLD_HEIGHT - 1) {  // Don't clear the ground line
            // Clear the line
            for (int x = 0; x < WORLD_WIDTH; x++) {
                world->grid[y][x] = BRICK_EMPTY;
            }
            
            // Move all lines above down by one
            for (int move_y = y; move_y > 0; move_y--) {
                for (int x = 0; x < WORLD_WIDTH; x++) {
                    world->grid[move_y][x] = world->grid[move_y - 1][x];
                }
            }
            
            // Clear top line
            for (int x = 0; x < WORLD_WIDTH; x++) {
                world->grid[0][x] = BRICK_EMPTY;
            }
            
            lines_cleared++;
            y++;  // Check this line again since we moved everything down
        }
    }
    
    if (lines_cleared > 0) {
        world->lines_completed += lines_cleared;
        world->score += calculate_score(lines_cleared, world->level);
        
        // Level up every 10 lines
        world->level = (world->lines_completed / 10) + 1;
        
        // Give player more bricks as reward
        world->player.bricks_carried += lines_cleared * 2;
        if (world->player.bricks_carried > 10) {
            world->player.bricks_carried = 10;  // Cap at max capacity
        }
    }
}

void move_player(GameWorld* world, int dx, int dy) {
    int new_x = world->player.pos.x + dx;
    int new_y = world->player.pos.y + dy;
    
    if (is_player_position_valid(world, new_x, new_y)) {
        world->player.pos.x = new_x;
        world->player.pos.y = new_y;
    }
}

void reset_game(GameWorld* world) {
    init_game(world);
    world->state = GAME_PLAYING;
}

int calculate_score(int lines_completed, int level) {
    // Score increases with level and number of lines cleared simultaneously
    int base_score = 100;
    int line_multiplier = 1;
    
    switch (lines_completed) {
        case 1: line_multiplier = 1; break;
        case 2: line_multiplier = 3; break;
        case 3: line_multiplier = 5; break;
        case 4: line_multiplier = 8; break;
        default: line_multiplier = lines_completed * 2; break;
    }
    
    return base_score * line_multiplier * level;
}
