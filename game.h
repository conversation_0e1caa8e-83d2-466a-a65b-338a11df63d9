#ifndef GAME_H
#define GAME_H

#include <stdbool.h>

// Game constants
#define WORLD_WIDTH 20
#define WORLD_HEIGHT 15
#define MAX_BRICKS 100
#define GRAVITY_DELAY 500  // milliseconds

// Game states
typedef enum {
    GAME_MENU,
    GAME_PLAYING,
    GAME_PAUSED,
    GAME_OVER
} GameState;

// Brick types
typedef enum {
    BRICK_EMPTY = 0,
    BRICK_NORMAL = 1,
    BRICK_FALLING = 2
} BrickType;

// Position structure
typedef struct {
    int x;
    int y;
} Position;

// Brick structure
typedef struct {
    Position pos;
    BrickType type;
    bool is_falling;
    int fall_timer;
} Brick;

// Player structure
typedef struct {
    Position pos;
    int bricks_carried;
    bool can_place_brick;
} Player;

// Game world structure
typedef struct {
    BrickType grid[WORLD_HEIGHT][WORLD_WIDTH];
    Brick falling_bricks[MAX_BRICKS];
    int num_falling_bricks;
    Player player;
    GameState state;
    int score;
    int level;
    int lines_completed;
    long last_gravity_time;
} GameWorld;

// Function declarations
void init_game(GameWorld* world);
void update_game(GameWorld* world, long current_time);
bool is_valid_position(const GameWorld* world, int x, int y);
bool can_place_brick(const GameWorld* world, int x, int y);
void place_brick(GameWorld* world, int x, int y);
void apply_gravity(GameWorld* world);
void check_completed_lines(GameWorld* world);
void move_player(GameWorld* world, int dx, int dy);
void reset_game(GameWorld* world);
int calculate_score(int lines_completed, int level);

#endif // GAME_H
