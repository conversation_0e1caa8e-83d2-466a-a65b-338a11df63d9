#include "input.h"
#include "player.h"
#include <stdio.h>
#include <termios.h>
#include <unistd.h>
#include <fcntl.h>
#include <string.h>

static struct termios old_termios;
static bool input_initialized = false;
static InputState global_input_state;

void init_input_state(InputState* input) {
    memset(input->keys_pressed, false, sizeof(input->keys_pressed));
    memset(input->key_just_pressed, false, sizeof(input->key_just_pressed));
    memset(input->last_key_time, 0, sizeof(input->last_key_time));
    input->key_repeat_delay = 150; // 150ms repeat delay
}

void init_input(void) {
    if (input_initialized) return;

    // Initialize input state
    init_input_state(&global_input_state);

    // Save current terminal settings
    if (tcgetattr(STDIN_FILENO, &old_termios) != 0) {
        fprintf(stderr, "Warning: Could not get terminal attributes\n");
        return;
    }

    // Set terminal to raw mode for immediate input
    struct termios new_termios = old_termios;
    new_termios.c_lflag &= ~(ICANON | ECHO);
    new_termios.c_cc[VMIN] = 0;  // Non-blocking read
    new_termios.c_cc[VTIME] = 0; // No timeout

    if (tcsetattr(STDIN_FILENO, TCSANOW, &new_termios) != 0) {
        fprintf(stderr, "Warning: Could not set terminal attributes\n");
        return;
    }

    // Set stdin to non-blocking
    int flags = fcntl(STDIN_FILENO, F_GETFL, 0);
    if (flags != -1) {
        fcntl(STDIN_FILENO, F_SETFL, flags | O_NONBLOCK);
    }

    input_initialized = true;
}

void cleanup_input(void) {
    if (!input_initialized) return;

    // Restore terminal settings
    if (tcsetattr(STDIN_FILENO, TCSANOW, &old_termios) != 0) {
        fprintf(stderr, "Warning: Could not restore terminal attributes\n");
    }

    // Restore stdin blocking mode
    int flags = fcntl(STDIN_FILENO, F_GETFL, 0);
    if (flags != -1) {
        fcntl(STDIN_FILENO, F_SETFL, flags & ~O_NONBLOCK);
    }

    input_initialized = false;
}

void update_input_state(InputState* input, KeyCode key, long current_time) {
    if (key == KEY_NONE || key >= 10) return;

    // Reset just_pressed flags
    for (int i = 0; i < 10; i++) {
        input->key_just_pressed[i] = false;
    }

    // Update key state
    if (!input->keys_pressed[key]) {
        input->key_just_pressed[key] = true;
        input->keys_pressed[key] = true;
        input->last_key_time[key] = current_time;
    } else {
        // Handle key repeat
        if (current_time - input->last_key_time[key] >= input->key_repeat_delay) {
            input->key_just_pressed[key] = true;
            input->last_key_time[key] = current_time;
        }
    }
}

bool is_key_just_pressed(const InputState* input, KeyCode key) {
    if (key == KEY_NONE || key >= 10) return false;
    return input->key_just_pressed[key];
}

bool should_process_key_repeat(const InputState* input, KeyCode key, long current_time) {
    if (key == KEY_NONE || key >= 10) return false;
    return input->keys_pressed[key] &&
           (current_time - input->last_key_time[key] >= input->key_repeat_delay);
}

KeyCode get_key_input(void) {
    char c;
    ssize_t bytes_read = read(STDIN_FILENO, &c, 1);

    if (bytes_read == 1) {
        switch (c) {
            case 'w':
            case 'W':
                return KEY_UP;
            case 's':
            case 'S':
                return KEY_DOWN;
            case 'a':
            case 'A':
                return KEY_LEFT;
            case 'd':
            case 'D':
                return KEY_RIGHT;
            case ' ':
                return KEY_SPACE;
            case '\n':
            case '\r':
                return KEY_ENTER;
            case 27:  // ESC
                return KEY_ESC;
            case 'q':
            case 'Q':
                return KEY_Q;
            case 'r':
            case 'R':
                return KEY_R;
            default:
                return KEY_NONE;
        }
    } else if (bytes_read == -1) {
        // No input available (non-blocking read)
        // Reset all key states
        for (int i = 0; i < 10; i++) {
            global_input_state.keys_pressed[i] = false;
            global_input_state.key_just_pressed[i] = false;
        }
    }

    return KEY_NONE;
}

void process_input(GameWorld* world, KeyCode key, long current_time) {
    if (key == KEY_NONE) return;

    // Update input state
    update_input_state(&global_input_state, key, current_time);

    switch (world->state) {
        case GAME_MENU:
            if (is_key_just_pressed(&global_input_state, KEY_ENTER)) {
                world->state = GAME_PLAYING;
            } else if (is_key_just_pressed(&global_input_state, KEY_Q) ||
                      is_key_just_pressed(&global_input_state, KEY_ESC)) {
                world->state = GAME_OVER;
            }
            break;

        case GAME_PLAYING:
            // Movement keys can repeat
            if (global_input_state.keys_pressed[KEY_UP]) {
                move_player_direction(world, MOVE_UP);
            }
            if (global_input_state.keys_pressed[KEY_DOWN]) {
                move_player_direction(world, MOVE_DOWN);
            }
            if (global_input_state.keys_pressed[KEY_LEFT]) {
                move_player_direction(world, MOVE_LEFT);
            }
            if (global_input_state.keys_pressed[KEY_RIGHT]) {
                move_player_direction(world, MOVE_RIGHT);
            }

            // Action keys should only trigger once
            if (is_key_just_pressed(&global_input_state, KEY_SPACE)) {
                player_place_brick(world);
            }
            if (is_key_just_pressed(&global_input_state, KEY_ESC)) {
                world->state = GAME_PAUSED;
            }
            if (is_key_just_pressed(&global_input_state, KEY_Q)) {
                world->state = GAME_OVER;
            }
            if (is_key_just_pressed(&global_input_state, KEY_R)) {
                reset_game(world);
            }
            break;

        case GAME_PAUSED:
            if (is_key_just_pressed(&global_input_state, KEY_ESC) ||
                is_key_just_pressed(&global_input_state, KEY_ENTER)) {
                world->state = GAME_PLAYING;
            } else if (is_key_just_pressed(&global_input_state, KEY_Q)) {
                world->state = GAME_OVER;
            }
            break;

        case GAME_OVER:
            if (is_key_just_pressed(&global_input_state, KEY_R)) {
                reset_game(world);
                world->state = GAME_PLAYING;
            } else if (is_key_just_pressed(&global_input_state, KEY_Q) ||
                      is_key_just_pressed(&global_input_state, KEY_ESC)) {
                // Mark for exit - will be handled in main loop
                world->state = GAME_OVER;
            }
            break;
    }
}
