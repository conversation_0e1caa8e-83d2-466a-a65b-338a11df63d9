#include "input.h"
#include "player.h"
#include <stdio.h>
#include <termios.h>
#include <unistd.h>
#include <fcntl.h>

static struct termios old_termios;
static bool input_initialized = false;

void init_input(void) {
    if (input_initialized) return;
    
    // Save current terminal settings
    tcgetattr(STDIN_FILENO, &old_termios);
    
    // Set terminal to raw mode for immediate input
    struct termios new_termios = old_termios;
    new_termios.c_lflag &= ~(ICANON | ECHO);
    tcsetattr(STDIN_FILENO, TCSANOW, &new_termios);
    
    // Set stdin to non-blocking
    int flags = fcntl(STDIN_FILENO, F_GETFL, 0);
    fcntl(STDIN_FILENO, F_SETFL, flags | O_NONBLOCK);
    
    input_initialized = true;
}

void cleanup_input(void) {
    if (!input_initialized) return;
    
    // Restore terminal settings
    tcsetattr(STDIN_FILENO, TCSANOW, &old_termios);
    
    // Restore stdin blocking mode
    int flags = fcntl(STDIN_FILENO, F_GETFL, 0);
    fcntl(STDIN_FILENO, F_SETFL, flags & ~O_NONBLOCK);
    
    input_initialized = false;
}

KeyCode get_key_input(void) {
    char c;
    if (read(STDIN_FILENO, &c, 1) == 1) {
        switch (c) {
            case 'w':
            case 'W':
                return KEY_UP;
            case 's':
            case 'S':
                return KEY_DOWN;
            case 'a':
            case 'A':
                return KEY_LEFT;
            case 'd':
            case 'D':
                return KEY_RIGHT;
            case ' ':
                return KEY_SPACE;
            case '\n':
            case '\r':
                return KEY_ENTER;
            case 27:  // ESC
                return KEY_ESC;
            case 'q':
            case 'Q':
                return KEY_Q;
            case 'r':
            case 'R':
                return KEY_R;
            default:
                return KEY_NONE;
        }
    }
    return KEY_NONE;
}

void process_input(GameWorld* world, KeyCode key) {
    switch (world->state) {
        case GAME_MENU:
            if (key == KEY_ENTER) {
                world->state = GAME_PLAYING;
            } else if (key == KEY_Q || key == KEY_ESC) {
                world->state = GAME_OVER;
            }
            break;
            
        case GAME_PLAYING:
            switch (key) {
                case KEY_UP:
                    move_player_direction(world, MOVE_UP);
                    break;
                case KEY_DOWN:
                    move_player_direction(world, MOVE_DOWN);
                    break;
                case KEY_LEFT:
                    move_player_direction(world, MOVE_LEFT);
                    break;
                case KEY_RIGHT:
                    move_player_direction(world, MOVE_RIGHT);
                    break;
                case KEY_SPACE:
                    player_place_brick(world);
                    break;
                case KEY_ESC:
                    world->state = GAME_PAUSED;
                    break;
                case KEY_Q:
                    world->state = GAME_OVER;
                    break;
                case KEY_R:
                    reset_game(world);
                    break;
            }
            break;
            
        case GAME_PAUSED:
            if (key == KEY_ESC || key == KEY_ENTER) {
                world->state = GAME_PLAYING;
            } else if (key == KEY_Q) {
                world->state = GAME_OVER;
            }
            break;
            
        case GAME_OVER:
            if (key == KEY_R) {
                reset_game(world);
                world->state = GAME_PLAYING;
            } else if (key == KEY_Q || key == KEY_ESC) {
                // Exit game
            }
            break;
    }
}
