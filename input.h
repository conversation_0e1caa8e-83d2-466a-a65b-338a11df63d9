#ifndef INPUT_H
#define INPUT_H

#include "game.h"
#include <stdbool.h>

// Key codes for cross-platform compatibility
typedef enum {
    KEY_NONE = 0,
    KEY_UP = 1,
    KEY_DOWN = 2,
    KEY_LEFT = 3,
    KEY_RIGHT = 4,
    KEY_SPACE = 5,
    KEY_ENTER = 6,
    KEY_ESC = 7,
    KEY_Q = 8,
    KEY_R = 9
} KeyCode;

// Input state
typedef struct {
    bool keys_pressed[10];
    bool key_just_pressed[10];
    long last_key_time[10];
    long key_repeat_delay;
} InputState;

// Function declarations
void init_input(void);
void cleanup_input(void);
KeyCode get_key_input(void);
void process_input(GameWorld* world, KeyCode key, long current_time);
void update_input_state(InputState* input, KeyCode key, long current_time);
bool is_key_just_pressed(const InputState* input, KeyCode key);
bool should_process_key_repeat(const InputState* input, KeyCode key, long current_time);
void init_input_state(InputState* input);

#endif // INPUT_H
