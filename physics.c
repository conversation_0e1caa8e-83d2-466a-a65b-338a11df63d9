#include "physics.h"
#include <stdio.h>

bool check_collision(const GameWorld* world, int x, int y) {
    // Check bounds
    if (x < 0 || x >= WORLD_WIDTH || y < 0 || y >= WORLD_HEIGHT) {
        return true;  // Collision with world boundary
    }
    
    // Check if position is occupied
    return world->grid[y][x] != BRICK_EMPTY;
}

bool is_position_solid(const GameWorld* world, int x, int y) {
    // Check bounds - treat out of bounds as solid
    if (x < 0 || x >= WORLD_WIDTH || y >= WORLD_HEIGHT) {
        return true;
    }
    
    // Top of world is not solid
    if (y < 0) {
        return false;
    }
    
    // Check if there's a brick at this position
    return world->grid[y][x] != BRICK_EMPTY;
}

void add_falling_brick(GameWorld* world, int x, int y) {
    if (!world) return;

    if (world->num_falling_bricks >= MAX_BRICKS) {
        return;  // Can't add more falling bricks
    }

    // Validate position
    if (x < 0 || x >= WORLD_WIDTH || y < 0 || y >= WORLD_HEIGHT) {
        return;
    }

    Brick* brick = &world->falling_bricks[world->num_falling_bricks];
    brick->pos.x = x;
    brick->pos.y = y;
    brick->type = BRICK_FALLING;
    brick->is_falling = true;
    brick->fall_timer = 0;

    world->num_falling_bricks++;
}

void remove_falling_brick(GameWorld* world, int index) {
    if (!world || index < 0 || index >= world->num_falling_bricks) {
        return;
    }

    // Move last brick to this position to avoid gaps
    if (index < world->num_falling_bricks - 1) {
        world->falling_bricks[index] = world->falling_bricks[world->num_falling_bricks - 1];
    }
    world->num_falling_bricks--;
}

bool can_brick_fall(const GameWorld* world, int x, int y) {
    // Check if there's space below
    return !is_position_solid(world, x, y + 1);
}

void settle_falling_brick(GameWorld* world, int index) {
    if (index < 0 || index >= world->num_falling_bricks) {
        return;
    }
    
    Brick* brick = &world->falling_bricks[index];
    
    // Place brick in grid
    if (brick->pos.y >= 0 && brick->pos.y < WORLD_HEIGHT &&
        brick->pos.x >= 0 && brick->pos.x < WORLD_WIDTH) {
        world->grid[brick->pos.y][brick->pos.x] = BRICK_NORMAL;
    }
    
    // Remove from falling bricks array
    remove_falling_brick(world, index);
}

void apply_gravity_to_falling_bricks(GameWorld* world) {
    for (int i = world->num_falling_bricks - 1; i >= 0; i--) {
        Brick* brick = &world->falling_bricks[i];
        
        if (can_brick_fall(world, brick->pos.x, brick->pos.y)) {
            // Move brick down
            brick->pos.y += GRAVITY_SPEED;
            
            // Check if brick went out of bounds (fell off bottom)
            if (brick->pos.y >= WORLD_HEIGHT) {
                remove_falling_brick(world, i);
                continue;
            }
        } else {
            // Brick can't fall anymore, settle it
            settle_falling_brick(world, i);
        }
    }
}

void check_brick_stability(GameWorld* world) {
    // Check all placed bricks to see if they should fall
    for (int y = WORLD_HEIGHT - 2; y >= 0; y--) {  // Start from second-to-bottom row
        for (int x = 0; x < WORLD_WIDTH; x++) {
            if (world->grid[y][x] == BRICK_NORMAL) {
                // Check if brick has support below
                if (can_brick_fall(world, x, y)) {
                    // Make brick fall
                    world->grid[y][x] = BRICK_EMPTY;
                    add_falling_brick(world, x, y);
                }
            }
        }
    }
}

void make_unsupported_bricks_fall(GameWorld* world) {
    bool changes_made = true;
    
    // Keep checking until no more bricks need to fall
    while (changes_made) {
        changes_made = false;
        
        for (int y = WORLD_HEIGHT - 2; y >= 0; y--) {
            for (int x = 0; x < WORLD_WIDTH; x++) {
                if (world->grid[y][x] == BRICK_NORMAL) {
                    // Check if brick is supported
                    bool has_support = false;
                    
                    // Check directly below
                    if (y + 1 < WORLD_HEIGHT && world->grid[y + 1][x] != BRICK_EMPTY) {
                        has_support = true;
                    }
                    
                    // If at bottom row, it's supported by the ground
                    if (y == WORLD_HEIGHT - 1) {
                        has_support = true;
                    }
                    
                    if (!has_support) {
                        world->grid[y][x] = BRICK_EMPTY;
                        add_falling_brick(world, x, y);
                        changes_made = true;
                    }
                }
            }
        }
    }
}
