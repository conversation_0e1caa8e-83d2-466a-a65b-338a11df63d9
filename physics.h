#ifndef PHYSICS_H
#define PHYSICS_H

#include "game.h"
#include <stdbool.h>

// Physics constants
#define GRAVITY_SPEED 1  // How fast bricks fall (cells per gravity tick)

// Function declarations
bool check_collision(const GameWorld* world, int x, int y);
bool is_position_solid(const GameWorld* world, int x, int y);
void apply_gravity_to_falling_bricks(GameWorld* world);
void add_falling_brick(GameWorld* world, int x, int y);
void remove_falling_brick(GameWorld* world, int index);
bool can_brick_fall(const GameWorld* world, int x, int y);
void settle_falling_brick(GameWorld* world, int index);
void check_brick_stability(GameWorld* world);
void make_unsupported_bricks_fall(GameWorld* world);

#endif // PHYSICS_H
