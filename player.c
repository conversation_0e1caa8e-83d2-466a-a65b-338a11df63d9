#include "player.h"
#include <stdio.h>

void init_player(Player* player) {
    player->pos.x = WORLD_WIDTH / 2;
    player->pos.y = WORLD_HEIGHT - 2;  // Start near bottom
    player->bricks_carried = 5;  // Start with some bricks
    player->can_place_brick = true;
}

bool is_player_position_valid(const GameWorld* world, int x, int y) {
    // Check bounds
    if (x < 0 || x >= WORLD_WIDTH || y < 0 || y >= WORLD_HEIGHT) {
        return false;
    }
    
    // Check if position is occupied by a brick
    if (world->grid[y][x] != BRICK_EMPTY) {
        return false;
    }
    
    return true;
}

bool move_player_direction(GameWorld* world, Direction dir) {
    int new_x = world->player.pos.x;
    int new_y = world->player.pos.y;
    
    switch (dir) {
        case MOVE_UP:
            new_y--;
            break;
        case MOVE_DOWN:
            new_y++;
            break;
        case MOVE_LEFT:
            new_x--;
            break;
        case MOVE_RIGHT:
            new_x++;
            break;
    }
    
    if (is_player_position_valid(world, new_x, new_y)) {
        world->player.pos.x = new_x;
        world->player.pos.y = new_y;
        return true;
    }
    
    return false;
}

bool player_place_brick(GameWorld* world) {
    Player* player = &world->player;
    
    // Check if player has bricks and can place
    if (player->bricks_carried <= 0 || !player->can_place_brick) {
        return false;
    }
    
    // Try to place brick below player
    int brick_x = player->pos.x;
    int brick_y = player->pos.y + 1;
    
    if (can_place_brick(world, brick_x, brick_y)) {
        place_brick(world, brick_x, brick_y);
        player->bricks_carried--;
        return true;
    }
    
    return false;
}

void player_pickup_brick(Player* player) {
    if (player->bricks_carried < 10) {  // Max carrying capacity
        player->bricks_carried++;
    }
}
