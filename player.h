#ifndef PLAYER_H
#define PLAYER_H

#include "game.h"

// Player movement directions
typedef enum {
    MOVE_UP,
    MOVE_DOWN,
    MOVE_LEFT,
    MOVE_RIGHT
} Direction;

// Function declarations
void init_player(Player* player);
bool move_player_direction(GameWorld* world, Direction dir);
bool player_place_brick(GameWorld* world);
void player_pickup_brick(Player* player);
bool is_player_position_valid(const GameWorld* world, int x, int y);

#endif // PLAYER_H
