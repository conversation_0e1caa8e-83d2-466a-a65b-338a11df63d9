#include "renderer.h"
#include <stdio.h>
#include <string.h>

void init_renderer(void) {
    // Initialize any renderer-specific settings
    clear_screen();
}

void cleanup_renderer(void) {
    clear_screen();
    printf("Thanks for playing Bricklayer!\n");
}

void clear_screen(void) {
    printf("\033[2J\033[H");  // ANSI escape codes to clear screen and move cursor to top
    fflush(stdout);
}

void render_game(const GameWorld* world) {
    clear_screen();
    
    switch (world->state) {
        case GAME_MENU:
            render_menu();
            break;
        case GAME_PLAYING:
            render_hud(world);
            render_world(world);
            render_instructions();
            break;
        case GAME_PAUSED:
            render_hud(world);
            render_world(world);
            render_paused();
            break;
        case GAME_OVER:
            render_game_over(world);
            break;
    }
    
    fflush(stdout);
}

void render_menu(void) {
    printf("╔══════════════════════════════════════╗\n");
    printf("║            BRICKLAYER GAME           ║\n");
    printf("║                                      ║\n");
    printf("║  Build structures by placing bricks! ║\n");
    printf("║                                      ║\n");
    printf("║  Controls:                           ║\n");
    printf("║  WASD - Move player                  ║\n");
    printf("║  SPACE - Place brick                 ║\n");
    printf("║  ESC - Pause game                    ║\n");
    printf("║  R - Restart game                    ║\n");
    printf("║  Q - Quit                            ║\n");
    printf("║                                      ║\n");
    printf("║  Press ENTER to start!               ║\n");
    printf("║  Press Q to quit                     ║\n");
    printf("╚══════════════════════════════════════╝\n");
}

void render_game_over(const GameWorld* world) {
    printf("╔══════════════════════════════════════╗\n");
    printf("║              GAME OVER               ║\n");
    printf("║                                      ║\n");
    printf("║  Final Score: %-6d               ║\n", world->score);
    printf("║  Level Reached: %-2d                 ║\n", world->level);
    printf("║  Lines Completed: %-3d              ║\n", world->lines_completed);
    printf("║                                      ║\n");
    printf("║  Press R to restart                  ║\n");
    printf("║  Press Q to quit                     ║\n");
    printf("╚══════════════════════════════════════╝\n");
}

void render_paused(void) {
    printf("\n");
    printf("╔══════════════════════════════════════╗\n");
    printf("║              GAME PAUSED             ║\n");
    printf("║                                      ║\n");
    printf("║  Press ESC or ENTER to continue      ║\n");
    printf("║  Press Q to quit                     ║\n");
    printf("╚══════════════════════════════════════╝\n");
}

void render_hud(const GameWorld* world) {
    printf("Score: %-6d | Level: %-2d | Lines: %-3d | Bricks: %-2d\n",
           world->score, world->level, world->lines_completed, 
           world->player.bricks_carried);
    printf("═══════════════════════════════════════════════════\n");
}

void render_world(const GameWorld* world) {
    // Top border
    printf("╔");
    for (int x = 0; x < WORLD_WIDTH; x++) {
        printf("═");
    }
    printf("╗\n");
    
    // Game world
    for (int y = 0; y < WORLD_HEIGHT; y++) {
        printf("║");
        for (int x = 0; x < WORLD_WIDTH; x++) {
            char display_char = CHAR_EMPTY;
            
            // Check if player is at this position
            if (world->player.pos.x == x && world->player.pos.y == y) {
                display_char = CHAR_PLAYER;
            }
            // Check for falling bricks
            else {
                bool found_falling = false;
                for (int i = 0; i < world->num_falling_bricks; i++) {
                    if (world->falling_bricks[i].pos.x == x && 
                        world->falling_bricks[i].pos.y == y) {
                        display_char = CHAR_FALLING_BRICK;
                        found_falling = true;
                        break;
                    }
                }
                
                // Check grid for placed bricks
                if (!found_falling) {
                    switch (world->grid[y][x]) {
                        case BRICK_EMPTY:
                            display_char = CHAR_EMPTY;
                            break;
                        case BRICK_NORMAL:
                            display_char = CHAR_BRICK;
                            break;
                        case BRICK_FALLING:
                            display_char = CHAR_FALLING_BRICK;
                            break;
                    }
                }
            }
            
            printf("%c", display_char);
        }
        printf("║\n");
    }
    
    // Bottom border
    printf("╚");
    for (int x = 0; x < WORLD_WIDTH; x++) {
        printf("═");
    }
    printf("╝\n");
}

void render_instructions(void) {
    printf("\nControls: WASD=Move, SPACE=Place Brick, ESC=Pause, R=Restart, Q=Quit\n");
}
