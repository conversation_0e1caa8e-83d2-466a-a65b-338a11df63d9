#ifndef RENDERER_H
#define RENDERER_H

#include "game.h"

// Display characters
#define CHAR_EMPTY ' '
#define CHAR_BRICK '#'
#define CHAR_PLAYER '@'
#define CHAR_FALLING_BRICK '*'
#define CHAR_BORDER '|'
#define CHAR_FLOOR '-'

// Function declarations
void init_renderer(void);
void cleanup_renderer(void);
void clear_screen(void);
void render_game(const GameWorld* world);
void render_menu(void);
void render_game_over(const GameWorld* world);
void render_paused(void);
void render_hud(const GameWorld* world);
void render_world(const GameWorld* world);
void render_instructions(void);

#endif // RENDERER_H
