#include <stdio.h>
#include <assert.h>
#include <string.h>
#include "game.h"
#include "player.h"
#include "physics.h"

// Test counter
static int tests_run = 0;
static int tests_passed = 0;

#define TEST(name) \
    do { \
        printf("Running test: %s... ", #name); \
        tests_run++; \
        if (test_##name()) { \
            printf("PASSED\n"); \
            tests_passed++; \
        } else { \
            printf("FAILED\n"); \
        } \
    } while(0)

// Test game initialization
bool test_game_initialization() {
    GameWorld world;
    init_game(&world);
    
    // Check initial state
    if (world.state != GAME_MENU) return false;
    if (world.score != 0) return false;
    if (world.level != 1) return false;
    if (world.lines_completed != 0) return false;
    if (world.num_falling_bricks != 0) return false;
    
    // Check player initialization
    if (world.player.pos.x != WORLD_WIDTH / 2) return false;
    if (world.player.pos.y != WORLD_HEIGHT - 2) return false;
    if (world.player.bricks_carried != 5) return false;
    
    // Check ground is initialized
    for (int x = 0; x < WORLD_WIDTH; x++) {
        if (world.grid[WORLD_HEIGHT - 1][x] != BRICK_NORMAL) return false;
    }
    
    return true;
}

// Test player movement
bool test_player_movement() {
    GameWorld world;
    init_game(&world);
    world.state = GAME_PLAYING;
    
    int initial_x = world.player.pos.x;
    int initial_y = world.player.pos.y;
    
    // Test moving right
    bool moved = move_player_direction(&world, MOVE_RIGHT);
    if (!moved || world.player.pos.x != initial_x + 1) return false;
    
    // Test moving left
    moved = move_player_direction(&world, MOVE_LEFT);
    if (!moved || world.player.pos.x != initial_x) return false;
    
    // Test moving up
    moved = move_player_direction(&world, MOVE_UP);
    if (!moved || world.player.pos.y != initial_y - 1) return false;
    
    // Test boundary collision (try to move out of bounds)
    world.player.pos.x = 0;
    moved = move_player_direction(&world, MOVE_LEFT);
    if (moved) return false;  // Should not be able to move left from x=0
    
    return true;
}

// Test brick placement
bool test_brick_placement() {
    GameWorld world;
    init_game(&world);
    world.state = GAME_PLAYING;
    
    // Position player
    world.player.pos.x = 5;
    world.player.pos.y = WORLD_HEIGHT - 3;
    world.player.bricks_carried = 5;
    
    int initial_bricks = world.player.bricks_carried;
    
    // Test placing a brick
    bool placed = player_place_brick(&world);
    if (!placed) return false;
    if (world.player.bricks_carried != initial_bricks - 1) return false;
    if (world.grid[world.player.pos.y + 1][world.player.pos.x] != BRICK_NORMAL) return false;
    
    // Test can't place brick on occupied space
    placed = player_place_brick(&world);
    if (placed) return false;  // Should fail because space is occupied
    
    return true;
}

// Test collision detection
bool test_collision_detection() {
    GameWorld world;
    init_game(&world);
    
    // Test boundary collision
    if (!check_collision(&world, -1, 5)) return false;  // Left boundary
    if (!check_collision(&world, WORLD_WIDTH, 5)) return false;  // Right boundary
    if (!check_collision(&world, 5, -1)) return false;  // Top boundary
    if (!check_collision(&world, 5, WORLD_HEIGHT)) return false;  // Bottom boundary
    
    // Test brick collision
    world.grid[5][5] = BRICK_NORMAL;
    if (!check_collision(&world, 5, 5)) return false;
    
    // Test empty space
    if (check_collision(&world, 5, 4)) return false;  // Should be empty
    
    return true;
}

// Test position validation
bool test_position_validation() {
    GameWorld world;
    init_game(&world);
    
    // Test valid positions
    if (!is_valid_position(&world, 0, 0)) return false;
    if (!is_valid_position(&world, WORLD_WIDTH - 1, WORLD_HEIGHT - 1)) return false;
    
    // Test invalid positions
    if (is_valid_position(&world, -1, 0)) return false;
    if (is_valid_position(&world, WORLD_WIDTH, 0)) return false;
    if (is_valid_position(&world, 0, -1)) return false;
    if (is_valid_position(&world, 0, WORLD_HEIGHT)) return false;
    
    return true;
}

// Test brick placement validation
bool test_brick_placement_validation() {
    GameWorld world;
    init_game(&world);
    
    // Test valid placement
    if (!can_place_brick(&world, 5, 5)) return false;
    
    // Test invalid placement on occupied space
    world.grid[5][5] = BRICK_NORMAL;
    if (can_place_brick(&world, 5, 5)) return false;
    
    // Test invalid placement on player position
    if (can_place_brick(&world, world.player.pos.x, world.player.pos.y)) return false;
    
    // Test invalid placement out of bounds
    if (can_place_brick(&world, -1, 5)) return false;
    if (can_place_brick(&world, WORLD_WIDTH, 5)) return false;
    
    return true;
}

// Test scoring system
bool test_scoring_system() {
    // Test score calculation
    int score1 = calculate_score(1, 1);  // 1 line, level 1
    int score2 = calculate_score(2, 1);  // 2 lines, level 1
    int score3 = calculate_score(1, 2);  // 1 line, level 2
    
    if (score1 != 100) return false;  // 100 * 1 * 1
    if (score2 != 300) return false;  // 100 * 3 * 1
    if (score3 != 200) return false;  // 100 * 1 * 2
    
    return true;
}

// Test physics - solid position detection
bool test_physics_solid_detection() {
    GameWorld world;
    init_game(&world);
    
    // Test ground is solid
    if (!is_position_solid(&world, 5, WORLD_HEIGHT - 1)) return false;
    
    // Test empty space is not solid
    if (is_position_solid(&world, 5, 5)) return false;
    
    // Test out of bounds behavior
    if (!is_position_solid(&world, -1, 5)) return false;  // Left boundary
    if (!is_position_solid(&world, WORLD_WIDTH, 5)) return false;  // Right boundary
    if (!is_position_solid(&world, 5, WORLD_HEIGHT)) return false;  // Bottom boundary
    if (is_position_solid(&world, 5, -1)) return false;  // Top boundary (not solid)
    
    return true;
}

// Test falling brick mechanics
bool test_falling_bricks() {
    GameWorld world;
    init_game(&world);
    
    // Add a falling brick
    add_falling_brick(&world, 5, 5);
    if (world.num_falling_bricks != 1) return false;
    if (world.falling_bricks[0].pos.x != 5) return false;
    if (world.falling_bricks[0].pos.y != 5) return false;
    
    // Test can brick fall
    if (!can_brick_fall(&world, 5, 5)) return false;  // Should be able to fall
    if (can_brick_fall(&world, 5, WORLD_HEIGHT - 2)) return false;  // Above ground, can't fall
    
    return true;
}

int main() {
    printf("Running Bricklayer Game Tests\n");
    printf("=============================\n\n");
    
    // Run all tests
    TEST(game_initialization);
    TEST(player_movement);
    TEST(brick_placement);
    TEST(collision_detection);
    TEST(position_validation);
    TEST(brick_placement_validation);
    TEST(scoring_system);
    TEST(physics_solid_detection);
    TEST(falling_bricks);
    
    // Print results
    printf("\n=============================\n");
    printf("Tests completed: %d/%d passed\n", tests_passed, tests_run);
    
    if (tests_passed == tests_run) {
        printf("All tests PASSED! ✓\n");
        return 0;
    } else {
        printf("Some tests FAILED! ✗\n");
        return 1;
    }
}
